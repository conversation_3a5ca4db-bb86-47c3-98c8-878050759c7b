
# Stable Diffusion Integration for SpireClub

This document explains how to set up and use the Stable Diffusion integration in the SpireClub application.

## Overview

The SpireClub application now includes full Stable Diffusion integration that allows users to:

1. **Create AI Models** with custom Stable Diffusion configurations
2. **Generate Images** using natural language prompts
3. **Configure Advanced Settings** like inference steps, guidance scale, and image dimensions
4. **Download Generated Images** directly from the interface

## Features

### Enhanced Model Creation
- **Model Selection**: Choose from multiple Stable Diffusion models (SDXL, SD 1.5, SD 2.1)
- **Negative Prompts**: Specify what you don't want in generated images
- **Image Dimensions**: Select from common aspect ratios (512x512, 1024x1024, etc.)
- **Advanced Settings**: Configure inference steps, guidance scale, seed, and safety checker

### Real-time Image Generation
- **Interactive Chat Interface**: Send prompts and receive generated images
- **Loading States**: Visual feedback during image generation
- **Image Display**: High-quality image preview with download functionality
- **Error Handling**: Graceful fallback when API calls fail

### API Integration
- **Multiple Providers**: Support for Stability AI and Replicate APIs
- **Fallback System**: Mock responses when no API keys are configured
- **Environment Configuration**: Easy setup through environment variables

## Setup Instructions

### 1. Choose Your Stable Diffusion Provider

#### Option A: Stability AI (Recommended for Production)
1. Sign up at [Stability AI](https://platform.stability.ai/)
2. Get your API key from the dashboard
3. Set environment variables:
```bash
STABLE_DIFFUSION_PROVIDER=stability
STABILITY_AI_API_KEY=your_api_key_here
```

#### Option B: Replicate (Good for Development)
1. Sign up at [Replicate](https://replicate.com/)
2. Get your API token from account settings
3. Set environment variables:
```bash
STABLE_DIFFUSION_PROVIDER=replicate
REPLICATE_API_TOKEN=your_token_here
```

### 2. Environment Configuration

Copy the example environment file:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your API credentials:
```bash
# Choose your provider
STABLE_DIFFUSION_PROVIDER=stability  # or 'replicate'

# Add your API key
STABILITY_AI_API_KEY=your_stability_ai_key
# OR
REPLICATE_API_TOKEN=your_replicate_token
```

### 3. Install Dependencies

The integration uses existing dependencies, but ensure you have:
```bash
npm install
```

### 4. Start the Development Server

```bash
npm run dev
```

## Usage Guide

### Creating an AI Model

1. Navigate to the "AI Models" tab in the dashboard
2. Click "Create New Model"
3. Fill in the model details:
   - **Name**: A descriptive name for your model
   - **Prompt**: The base prompt that describes what the model should generate
   - **Negative Prompt**: What to avoid in generations (optional)
   - **Model**: Choose your Stable Diffusion model
   - **Image Size**: Select dimensions for generated images
4. Configure advanced settings if needed:
   - **Inference Steps**: More steps = higher quality (10-50)
   - **Guidance Scale**: How closely to follow the prompt (1-20)
   - **Seed**: For reproducible results (-1 for random)
   - **Safety Checker**: Enable content filtering
5. Click "Create Model"

### Generating Images

1. Click "Connect" on any AI model card
2. In the chat interface, type your image prompt
3. Press Enter or click Send
4. Wait for the image to generate (typically 10-30 seconds)
5. View the generated image and download if desired

### Example Prompts

- "A serene mountain landscape at sunset with a lake reflection"
- "Portrait of a person in vintage 1920s fashion, art deco style"
- "Futuristic cityscape with flying cars and neon lights"
- "Abstract geometric patterns in vibrant colors"

## API Endpoints

### POST /api/models/create
Creates a new AI model with Stable Diffusion configuration.

**Request Body:**
```json
{
  "name": "Model Name",
  "prompt": "Base prompt",
  "negativePrompt": "Negative prompt",
  "model": "stable-diffusion-xl-base-1.0",
  "steps": 20,
  "guidanceScale": 7.5,
  "width": 1024,
  "height": 1024,
  "seed": -1,
  "enableSafetyChecker": true
}
```

### POST /api/models/generate
Generates an image using a configured model.

**Request Body:**
```json
{
  "modelId": "model_id",
  "prompt": "Image description",
  "negativePrompt": "What to avoid",
  "steps": 20,
  "guidanceScale": 7.5,
  "seed": 12345
}
```

## Troubleshooting

### No Images Generated
- Check that your API keys are correctly set in `.env.local`
- Verify your API provider has sufficient credits/quota
- Check the browser console for error messages

### Slow Generation Times
- Reduce the number of inference steps (try 15-20 instead of 50)
- Use smaller image dimensions
- Consider switching to a faster model

### API Errors
- Verify your API key is valid and has sufficient quota
- Check that your prompts don't violate content policies
- Ensure your environment variables are properly set

## Development Notes

### File Structure
- `components/create-model-modal.tsx` - Enhanced model creation interface
- `components/use-model-modal.tsx` - Image generation chat interface
- `app/api/models/create/route.ts` - Model creation API
- `app/api/models/generate/route.ts` - Image generation API
- `lib/stable-diffusion.ts` - Stable Diffusion client utilities

### Extending the Integration
- Add support for additional providers (OpenAI DALL-E, Midjourney, etc.)
- Implement model fine-tuning capabilities
- Add batch generation features
- Integrate with cloud storage for image persistence

## Security Considerations

- API keys are stored securely in environment variables
- Content filtering is enabled by default
- Generated images are not permanently stored (implement cloud storage if needed)
- Rate limiting should be implemented for production use

## Cost Optimization

- Use appropriate inference steps (20-30 is usually sufficient)
- Choose optimal image dimensions for your use case
- Implement caching for repeated prompts
- Monitor API usage and set up billing alerts

