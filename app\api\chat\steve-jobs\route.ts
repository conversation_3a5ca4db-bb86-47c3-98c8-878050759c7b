import { NextRequest, NextResponse } from "next/server";
import { chatWithSteveJobs, createOpenAIClient, getOpenAIConfig } from "@/lib/openai";
import type { ChatMessage } from "@/lib/openai";

interface ChatRequest {
  message: string;
  conversation?: ChatMessage[];
}

export async function POST(request: NextRequest) {
  console.log("🚀 Steve Jobs API endpoint called");
  console.log("Request method:", request.method);
  console.log("Request URL:", request.url);

  try {
    let body: ChatRequest;
    try {
      body = await request.json();
      console.log("✅ Request body parsed successfully:", {
        hasMessage: !!body.message,
        messageLength: body.message?.length || 0,
        hasConversation: !!body.conversation,
        conversationLength: body.conversation?.length || 0
      });
    } catch (parseError) {
      console.error("❌ Failed to parse request body:", parseError);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!body.message) {
      console.log("❌ No message provided in request");
      return NextResponse.json(
        { error: "Message is required" },
        { status: 400 }
      );
    }

    console.log("✅ Processing Steve Jobs chat request");

    let response;

    try {
      console.log("🔧 Checking OpenAI configuration...");
      const config = getOpenAIConfig();
      console.log("✅ Configuration loaded:", {
        hasApiKey: !!config.apiKey,
        apiKeyLength: config.apiKey?.length || 0,
        systemPromptLength: config.systemPrompt?.length || 0
      });

      if (body.conversation && body.conversation.length > 0) {
        console.log("💭 Continuing existing conversation...");
        // Continue existing conversation
        const client = createOpenAIClient();

        // Ensure system prompt is at the beginning
        const messages: ChatMessage[] = [
          {
            role: 'system',
            content: config.systemPrompt,
          },
          ...body.conversation,
          {
            role: 'user',
            content: body.message,
          },
        ];

        console.log("📤 Sending to OpenAI with", messages.length, "messages");
        response = await client.chat(messages);
      } else {
        console.log("🆕 Starting new conversation...");
        // Start new conversation with Steve Jobs
        response = await chatWithSteveJobs(body.message);
      }

      console.log("✅ Steve Jobs response generated successfully:", {
        messageLength: response.message?.length || 0,
        hasUsage: !!response.usage
      });
    } catch (apiError) {
      console.error("❌ OpenAI API error:", apiError);
      console.error("Error details:", {
        name: apiError instanceof Error ? apiError.name : 'Unknown',
        message: apiError instanceof Error ? apiError.message : String(apiError),
        stack: apiError instanceof Error ? apiError.stack : 'No stack trace'
      });

      return NextResponse.json(
        {
          error: "Failed to get response from Steve Jobs. Please check your OpenAI API configuration.",
          details: apiError instanceof Error ? apiError.message : String(apiError)
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: response.message,
      usage: response.usage,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("❌ Unexpected error in Steve Jobs chat:", error);
    console.error("Error details:", {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });

    return NextResponse.json(
      {
        error: "Failed to process chat request",
        details: error instanceof Error ? error.message : String(error),
        type: "server_error"
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check Steve Jobs chat status
export async function GET() {
  try {
    const { systemPrompt } = getOpenAIConfig();
    
    return NextResponse.json({
      status: "ready",
      configured: true,
      systemPromptLength: systemPrompt.length,
      message: "Steve Jobs chat is ready to use",
    });
  } catch (error) {
    return NextResponse.json({
      status: "not_configured",
      configured: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Steve Jobs chat is not properly configured",
    }, { status: 500 });
  }
}
