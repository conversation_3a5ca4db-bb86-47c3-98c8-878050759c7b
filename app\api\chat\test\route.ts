import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  console.log("🧪 Test chat endpoint called");
  
  try {
    const body = await request.json();
    console.log("✅ Body parsed:", body);
    
    return NextResponse.json({
      success: true,
      message: "This is a test response from Steve Jobs API",
      receivedMessage: body.message || "No message",
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error("❌ Test endpoint error:", error);
    return NextResponse.json({
      success: false,
      error: "Test endpoint failed",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    status: "Test chat endpoint is working",
    timestamp: new Date().toISOString()
  });
}
