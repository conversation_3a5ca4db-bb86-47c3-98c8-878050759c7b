import { NextRequest, NextResponse } from "next/server";
import { 
  getAllGenerations, 
  getGenerationsByModel, 
  getGenerationById 
} from "@/lib/database";

// GET /api/generations - Retrieve saved generations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const modelId = searchParams.get("modelId");
    const generationId = searchParams.get("generationId");
    const limit = searchParams.get("limit");

    // Get specific generation by ID
    if (generationId) {
      const generation = await getGenerationById(generationId);
      if (!generation) {
        return NextResponse.json(
          { error: "Generation not found" },
          { status: 404 }
        );
      }
      return NextResponse.json({ generation });
    }

    // Get generations by model ID
    if (modelId) {
      const generations = await getGenerationsByModel(modelId);
      return NextResponse.json({ 
        generations,
        count: generations.length 
      });
    }

    // Get all generations with optional limit
    const limitNum = limit ? parseInt(limit) : undefined;
    const generations = await getAllGenerations(limitNum);
    
    return NextResponse.json({ 
      generations,
      count: generations.length,
      total: generations.length 
    });

  } catch (error) {
    console.error("Error retrieving generations:", error);
    return NextResponse.json(
      { error: "Failed to retrieve generations" },
      { status: 500 }
    );
  }
}
