import { NextRequest, NextResponse } from "next/server";
import {
  createStableDiffusionClient,
  getStableDiffusionConfig,
  type StableDiffusionConfig,
} from "@/lib/stable-diffusion";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { saveGenerationRecord, type GenerationRecord } from "@/lib/database";

interface CreateModelRequest {
  name: string;
  prompt: string;
  negativePrompt?: string;
  model: string;
  steps: number;
  guidanceScale: number;
  width: number;
  height: number;
  seed: number;
  enableSafetyChecker: boolean;
}

// Function to save generated images to file system
async function saveImageToFile(
  base64Image: string,
  modelId: string,
  generationId: string
): Promise<{ url: string; filename: string }> {
  try {
    // Remove data URL prefix if present
    const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, "");

    if (!base64Data || base64Data === base64Image) {
      throw new Error("Invalid base64 image data - no data URL prefix found");
    }

    // Create directory structure: public/generated-images/modelId/
    const imagesDir = join(
      process.cwd(),
      "public",
      "generated-images",
      modelId
    );

    // Ensure directory exists
    await mkdir(imagesDir, { recursive: true });

    // Generate filename with timestamp
    const filename = `${generationId}_${Date.now()}.png`;
    const filepath = join(imagesDir, filename);

    // Save image file
    await writeFile(filepath, base64Data, "base64");

    // Return public URL path and filename
    const publicUrl = `/generated-images/${modelId}/${filename}`;
    return { url: publicUrl, filename };
  } catch (error) {
    console.error("Error saving image:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to save image: ${errorMessage}`);
  }
}

// Mock function for fallback - generates a proper placeholder image
async function mockStableDiffusionAPI(config: StableDiffusionConfig) {
  console.log("Using mock Stable Diffusion API for prompt:", config.prompt);
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // Create a proper placeholder image with text
  const canvas = createMockImage(config.width, config.height, config.prompt);
  const mockBase64Image = canvas;

  return {
    images: [mockBase64Image],
    parameters: {
      prompt: config.prompt,
      negative_prompt: config.negative_prompt,
      width: config.width,
      height: config.height,
      num_inference_steps: config.num_inference_steps,
      guidance_scale: config.guidance_scale,
      seed: config.seed || Math.floor(Math.random() * 1000000),
    },
  };
}

// Create a mock image with text overlay
function createMockImage(
  width: number,
  height: number,
  prompt: string
): string {
  // Create a simple SVG placeholder image
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)"/>
      <rect x="20" y="20" width="${width - 40}" height="${
    height - 40
  }" fill="none" stroke="white" stroke-width="2" stroke-dasharray="10,5"/>
      <text x="50%" y="35%" dominant-baseline="middle" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${Math.max(
        24,
        width / 100
      )}" font-weight="bold">
        4K MOCK IMAGE
      </text>
      <text x="50%" y="45%" dominant-baseline="middle" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${Math.max(
        16,
        width / 150
      )}">
        Generated from prompt:
      </text>
      <text x="50%" y="55%" dominant-baseline="middle" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${Math.max(
        14,
        width / 200
      )}">
        "${prompt.length > 60 ? prompt.substring(0, 60) + "..." : prompt}"
      </text>
      <text x="50%" y="70%" dominant-baseline="middle" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-family="Arial, sans-serif" font-size="${Math.max(
        12,
        width / 250
      )}">
        ${width} × ${height} pixels (4K Resolution)
      </text>
      <text x="50%" y="80%" dominant-baseline="middle" text-anchor="middle" fill="rgba(255,255,255,0.5)" font-family="Arial, sans-serif" font-size="${Math.max(
        10,
        width / 300
      )}">
        Configure Stability AI API key for real 4K generation
      </text>
    </svg>
  `;

  // Convert SVG to base64 data URL
  const base64Svg = Buffer.from(svg).toString("base64");
  return `data:image/svg+xml;base64,${base64Svg}`;
}

export async function POST(request: NextRequest) {
  try {
    const body: CreateModelRequest = await request.json();

    // Validate required fields
    if (!body.name || !body.prompt) {
      return NextResponse.json(
        { error: "Name and prompt are required" },
        { status: 400 }
      );
    }

    // Create Stable Diffusion configuration
    const stableDiffusionConfig: StableDiffusionConfig = {
      model: body.model,
      prompt: body.prompt,
      negative_prompt: body.negativePrompt || "",
      width: body.width,
      height: body.height,
      num_inference_steps: body.steps,
      guidance_scale: body.guidanceScale,
      seed: body.seed === -1 ? undefined : body.seed,
      safety_checker: body.enableSafetyChecker,
    };

    // Generate model ID
    const modelId = `model_${Date.now()}`;

    // Generate a preview image using the model's prompt
    console.log("Generating preview image for new model:", modelId);
    let previewImage = null;
    let generationResult = null;

    try {
      // Try to use real Stable Diffusion API if configured
      const { provider, apiKey } = getStableDiffusionConfig();
      const client = createStableDiffusionClient(provider, apiKey);
      // generationResult = await client.generateImage(stableDiffusionConfig);

      // const baseUrl = "https://api.stability.ai/v2beta/image/text-to-image";
      // const upscaleUrl =
      //   "https://api.stability.ai/v2beta/stable-image/upscale/fast";

      // const response = await fetch(`${baseUrl}/predictions`, {
      //   method: "POST",
      //   headers: {
      //     "Content-Type": "application/json",
      //     Authorization: `Token ${apiKey}`,
      //   },
      //   body: JSON.stringify({
      //     version: "stable-diffusion-xl-base-1.0",
      //     input: {
      //       prompt: stableDiffusionConfig.prompt,
      //       negative_prompt: stableDiffusionConfig.negative_prompt || "",
      //       width: stableDiffusionConfig.width,
      //       height: stableDiffusionConfig.height,
      //       num_inference_steps: stableDiffusionConfig.num_inference_steps,
      //       guidance_scale: stableDiffusionConfig.guidance_scale,
      //       seed: stableDiffusionConfig.seed,
      //     },
      //   }),
      // });

      // if (!response.ok) {
      //   throw new Error(`Replicate API error: ${response.statusText}`);
      // }

      // console.log("text2img : ", response.text);

      // const upscale_response = await fetch(`${upscaleUrl}`, {
      //   method: "POST",
      //   headers: {
      //     Accept: "image/*",
      //     Authorization: `Token ${apiKey}`,
      //   },
      //   body: JSON.stringify({
      //     version:
      //       "stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
      //     input: {
      //       prompt: stableDiffusionConfig.prompt,
      //       negative_prompt: stableDiffusionConfig.negative_prompt || "",
      //       width: 4096,
      //       height: 4096,
      //       num_inference_steps: stableDiffusionConfig.num_inference_steps,
      //       guidance_scale: stableDiffusionConfig.guidance_scale,
      //       seed: stableDiffusionConfig.seed,
      //     },
      //   }),
      // });

      // console.log("upscale : ", upscale_response.text);

      console.log("Generated preview using real Stable Diffusion API");
    } catch (configError) {
      console.warn(
        "Stable Diffusion API not configured, using mock response:",
        configError
      );
      // Fall back to mock API if no configuration is available
      generationResult = await mockStableDiffusionAPI(stableDiffusionConfig);
      console.log("Generated preview using mock Stable Diffusion API");
    }

    // Save the preview image
    if (generationResult && generationResult.images.length > 0) {
      try {
        const generationId = `preview_${Date.now()}`;
        const saveResult = await saveImageToFile(
          generationResult.images[0],
          modelId,
          generationId
        );

        previewImage = {
          url: saveResult.url,
          base64: generationResult.images[0],
          generationId: generationId,
        };

        // Save generation record to database
        const generationRecord: GenerationRecord = {
          id: generationId,
          modelId: modelId,
          prompt: body.prompt,
          negativePrompt: body.negativePrompt,
          parameters: {
            width: stableDiffusionConfig.width,
            height: stableDiffusionConfig.height,
            steps: stableDiffusionConfig.num_inference_steps,
            guidanceScale: stableDiffusionConfig.guidance_scale,
            seed: generationResult.parameters.seed,
            model: stableDiffusionConfig.model,
          },
          images: [
            {
              url: saveResult.url,
              filename: saveResult.filename,
            },
          ],
          generatedAt: new Date().toISOString(),
        };

        await saveGenerationRecord(generationRecord);
        console.log("Preview image saved and recorded");
      } catch (saveError) {
        console.error("Failed to save preview image:", saveError);
        // Continue even if preview save fails
      }
    }

    const createdModel = {
      id: modelId,
      name: body.name,
      prompt: body.prompt,
      negativePrompt: body.negativePrompt,
      config: stableDiffusionConfig,
      status: "online",
      createdAt: new Date().toISOString(),
      previewImage: previewImage,
    };

    return NextResponse.json({
      success: true,
      model: createdModel,
      message: previewImage
        ? "AI model created successfully with preview image generated!"
        : "AI model created successfully with Stable Diffusion integration",
      previewGenerated: !!previewImage,
    });
  } catch (error) {
    console.error("Error creating model:", error);
    return NextResponse.json(
      { error: "Failed to create model" },
      { status: 500 }
    );
  }
}
