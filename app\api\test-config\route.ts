import { NextRequest, NextResponse } from "next/server";

export async function GET() {
  try {
    console.log("🔧 Testing environment configuration...");

    const openaiKey = process.env.OPENAI_API_KEY;
    const steveJobsPrompt = process.env.STEVE_JOBS_SYSTEM_PROMPT;

    console.log("Environment variables check:", {
      hasOpenAIKey: !!openaiKey,
      openAIKeyLength: openaiKey?.length || 0,
      openAIKeyPrefix: openaiKey ? openaiKey.substring(0, 10) + "..." : "none",
      hasSteveJobsPrompt: !!steveJobsPrompt,
      steveJobsPromptLength: steveJobsPrompt?.length || 0,
    });

    // Test the OpenAI configuration function
    let configTest = null;
    try {
      const { getOpenAIConfig } = await import("@/lib/openai");
      const config = getOpenAIConfig();
      configTest = {
        success: true,
        hasApiKey: !!config.apiKey,
        hasSystemPrompt: !!config.systemPrompt,
        apiKeyLength: config.apiKey?.length || 0,
        systemPromptLength: config.systemPrompt?.length || 0
      };
    } catch (configError) {
      configTest = {
        success: false,
        error: configError instanceof Error ? configError.message : String(configError)
      };
    }

    return NextResponse.json({
      status: "success",
      config: {
        hasOpenAIKey: !!openaiKey,
        openAIKeyLength: openaiKey?.length || 0,
        openAIKeyPrefix: openaiKey ? openaiKey.substring(0, 10) + "..." : "none",
        hasSteveJobsPrompt: !!steveJobsPrompt,
        steveJobsPromptLength: steveJobsPrompt?.length || 0,
      },
      configTest: configTest,
      message: "Configuration test completed"
    });

  } catch (error) {
    console.error("❌ Configuration test error:", error);
    return NextResponse.json({
      status: "error",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
