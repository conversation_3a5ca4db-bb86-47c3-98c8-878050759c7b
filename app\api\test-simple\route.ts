import { NextRequest, NextResponse } from "next/server";

export async function GET() {
  console.log("Simple test endpoint called");
  return NextResponse.json({
    status: "success",
    message: "Simple test endpoint is working",
    timestamp: new Date().toISOString()
  });
}

export async function POST(request: NextRequest) {
  console.log("Simple test POST endpoint called");
  
  try {
    const body = await request.json();
    console.log("Request body:", body);
    
    return NextResponse.json({
      status: "success",
      message: "POST request received successfully",
      receivedData: body,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Error in simple test POST:", error);
    return NextResponse.json({
      status: "error",
      message: "Failed to process POST request",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
