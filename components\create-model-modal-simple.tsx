"use client";

import { useState } from "react";
import { Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface CreateModelModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreateModelModalSimple({
  isOpen,
  onClose,
}: CreateModelModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    prompt: "",
    negativePrompt: "",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    // Add your submission logic here
  };

  const handleClose = () => {
    setFormData({
      name: "",
      prompt: "",
      negativePrompt: "",
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            Create AI Model
            {formData.name && ` - ${formData.name}`}
          </DialogTitle>
          <DialogDescription>
            Create a custom AI model for generating personalized content
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Model Name */}
          <div className="space-y-2">
            <Label htmlFor="modelName">Model Name *</Label>
            <Input
              id="modelName"
              type="text"
              placeholder="Enter a name for your AI model"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              maxLength={50}
              required
            />
            <p className="text-xs text-gray-500">
              {formData.name.length}/50 characters
            </p>
          </div>

          {/* Prompt */}
          <div className="space-y-2">
            <Label htmlFor="prompt">AI Prompt *</Label>
            <Textarea
              id="prompt"
              placeholder="Describe what you want your AI model to generate..."
              value={formData.prompt}
              onChange={(e) => handleInputChange("prompt", e.target.value)}
              className="min-h-[80px]"
              maxLength={500}
              required
            />
            <p className="text-xs text-gray-500">
              {formData.prompt.length}/500 characters
            </p>
          </div>

          {/* Negative Prompt */}
          <div className="space-y-2">
            <Label htmlFor="negativePrompt">Negative Prompt</Label>
            <Textarea
              id="negativePrompt"
              placeholder="Describe what you don't want in the generated images"
              value={formData.negativePrompt}
              onChange={(e) => handleInputChange("negativePrompt", e.target.value)}
              className="min-h-[60px]"
              maxLength={300}
            />
            <p className="text-xs text-gray-500">
              {formData.negativePrompt.length}/300 characters
            </p>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!formData.name.trim() || !formData.prompt.trim()}
            >
              Create Model
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
