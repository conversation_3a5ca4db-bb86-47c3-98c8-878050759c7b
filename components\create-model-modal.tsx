"use client";

import type React from "react";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Wand2,
  Save,
  X,
  Image as ImageIcon,
  Settings,
  Download,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";

interface CreateModelModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CreatedModel {
  id: string;
  name: string;
  prompt: string;
  negativePrompt?: string;
  status: string;
  createdAt: string;
  previewImage?: {
    url: string;
    base64: string;
    generationId: string;
  };
}

export function CreateModelModal({ isOpen, onClose }: CreateModelModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    prompt: "",
    negativePrompt: "",
    model: "stable-diffusion-xl-base-1.0",
    steps: 20,
    guidanceScale: 7.5,
    width: 1024,
    height: 1024,
    seed: -1,
    enableSafetyChecker: true,
    autoUpscale: false,
  });
  const [isCreating, setIsCreating] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [createdModel, setCreatedModel] = useState<CreatedModel | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleInputChange = (
    field: string,
    value: string | number | boolean
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);

    try {
      // Create the AI model with Stable Diffusion configuration
      const response = await fetch("/api/models/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Failed to create photo");
      }

      const result = await response.json();
      console.log("Photo created successfully:", result);

      // Store the created model data and show success
      setCreatedModel(result.model);
      setShowSuccess(true);
      setIsCreating(false);

      // Auto-close after showing success for a few seconds
      setTimeout(() => {
        setShowSuccess(false);
        setCreatedModel(null);
        // Reset form and close modal
        setFormData({
          name: "",
          prompt: "",
          negativePrompt: "",
          model: "stable-diffusion-xl-base-1.0",
          steps: 20,
          guidanceScale: 7.5,
          width: 1024,
          height: 1024,
          seed: -1,
          enableSafetyChecker: true,
          autoUpscale: false,
        });
        onClose();
      }, 5000); // Show success for 5 seconds
    } catch (error) {
      console.error("Error creating photo:", error);
      setIsCreating(false);
      // You might want to show an error toast here
    }
  };

  const handleClose = () => {
    if (!isCreating) {
      setFormData({
        name: "",
        prompt: "",
        negativePrompt: "",
        model: "stable-diffusion-xl-base-1.0",
        steps: 20,
        guidanceScale: 7.5,
        width: 1024,
        height: 1024,
        seed: -1,
        enableSafetyChecker: true,
        autoUpscale: false,
      });
      setShowAdvanced(false);
      setShowSuccess(false);
      setCreatedModel(null);
      onClose();
    }
  };

  const isFormValid =
    formData.name.trim() !== "" && formData.prompt.trim() !== "";

  const availableModels = [
    {
      value: "stable-diffusion-xl-base-1.0",
      label: "Stable Diffusion XL Base 1.0",
    },
    { value: "stable-diffusion-v1-5", label: "Stable Diffusion v1.5" },
    { value: "stable-diffusion-2-1", label: "Stable Diffusion 2.1" },
  ];

  const imageSizes = [
    {
      value: "1024x1024",
      width: 1024,
      height: 1024,
      label: "4K Square (4096×4096)",
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-400 drop-shadow-lg" />
            Create Photo
            {formData.name && (
              <span className="text-sm font-normal text-gray-400 ml-2">
                - {formData.name}
              </span>
            )}
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Create a custom photo generator for realistic image creation
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 bg-gray-800/50 rounded-lg p-6 border border-gray-700/50">
            {showSuccess && createdModel ? (
              <div className="text-center space-y-6">
                <div className="flex items-center justify-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                    <Save className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-green-400">
                      Photo Created Successfully!
                    </h3>
                    <p className="text-gray-400 text-sm">
                      {createdModel.previewImage
                        ? "Preview photo generated and saved"
                        : "Photo configuration saved"}
                    </p>
                  </div>
                </div>
                {/* Preview Image Display */}
                {/* Model Details */}
              </div>
            ) : (
              // Regular Form View
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Basic Information Section */}
                <div className="space-y-4 p-4 bg-gray-800/30 rounded-lg border border-gray-700/50 hover:bg-gray-800/50 transition-colors">
                  <div className="flex items-center gap-2 text-sm font-medium text-purple-300 border-b border-purple-800/30 pb-2">
                    <Sparkles className="w-4 h-4 text-purple-400" />
                    Basic Information
                  </div>

                  {/* Model Name Field */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="modelName"
                      className="text-sm font-medium text-gray-200"
                    >
                      Photo Name *
                    </Label>
                    <Input
                      id="modelName"
                      type="text"
                      placeholder="Enter a name for your photo (e.g., 'Beach Sunset Portrait')"
                      value={formData.name}
                      onChange={(e) => handleInputChange("name", e.target.value)}
                      className="bg-gray-800 border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500"
                      disabled={isCreating}
                      maxLength={50}
                    />
                  <p className="text-xs text-gray-400">
                    {formData.name.length}/50 characters
                  </p>
                </div>

                {/* Prompt Field */}
                <div className="space-y-2">
                  <Label
                    htmlFor="prompt"
                    className="text-sm font-medium text-gray-200"
                  >
                    Photo Description *
                  </Label>
                  <Textarea
                    id="prompt"
                    placeholder="Describe the photo you want to create. Be specific about style, mood, setting, and any other details..."
                    value={formData.prompt}
                    onChange={(e) =>
                      handleInputChange("prompt", e.target.value)
                    }
                    className="bg-gray-800 border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500 min-h-[80px] resize-none"
                    disabled={isCreating}
                    maxLength={500}
                  />
                  <p className="text-xs text-gray-400">
                    {formData.prompt.length}/500 characters
                  </p>
                </div>

                {/* Negative Prompt Field */}
                <div className="space-y-2">
                  <Label
                    htmlFor="negativePrompt"
                    className="text-sm font-medium text-gray-200"
                  >
                    Negative Prompt
                  </Label>
                  <Textarea
                    id="negativePrompt"
                    placeholder="Describe what you don't want in the photo (e.g., 'blurry, low quality, distorted')"
                    value={formData.negativePrompt}
                    onChange={(e) =>
                      handleInputChange("negativePrompt", e.target.value)
                    }
                    className="bg-gray-800 border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500 min-h-[60px] resize-none"
                    disabled={isCreating}
                    maxLength={300}
                  />
                  <p className="text-xs text-gray-400">
                    {formData.negativePrompt.length}/300 characters
                  </p>
                </div>
                </div>

                {/* Configuration Section */}
                <div className="space-y-4 p-4 bg-gray-800/30 rounded-lg border border-gray-700/50 hover:bg-gray-800/50 transition-colors">
                  <div className="flex items-center gap-2 text-sm font-medium text-purple-300 border-b border-purple-800/30 pb-2">
                    <Settings className="w-4 h-4 text-purple-400" />
                    Photo Configuration
                  </div>

                  {/* Model Selection */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-200">
                    Stable Diffusion Model
                  </Label>
                  <Select
                    value={formData.model}
                    onValueChange={(value) => handleInputChange("model", value)}
                    disabled={isCreating}
                  >
                    <SelectTrigger className="bg-gray-800 border-gray-600 text-white focus:border-purple-500 focus:ring-purple-500">
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-600">
                      {availableModels.map((model) => (
                        <SelectItem
                          key={model.value}
                          value={model.value}
                          className="text-white hover:bg-gray-700"
                        >
                          {model.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Image Size Selection */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-200">
                    Image Size (4K Resolution)
                  </Label>
                  <Select
                    value={`${formData.width}x${formData.height}`}
                    onValueChange={(value) => {
                      const size = imageSizes.find((s) => s.value === value);
                      if (size) {
                        handleInputChange("width", size.width);
                        handleInputChange("height", size.height);
                      }
                    }}
                    disabled={isCreating}
                  >
                    <SelectTrigger className="bg-gray-800 border-gray-600 text-white focus:border-purple-500 focus:ring-purple-500">
                      <SelectValue placeholder="Select image size" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-600">
                      {imageSizes.map((size) => (
                        <SelectItem
                          key={size.value}
                          value={size.value}
                          className="text-white hover:bg-gray-700"
                        >
                          {size.label || size.value}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                </div>

                {/* Advanced Settings Toggle */}
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-200 flex items-center gap-2">
                    <Settings className="w-4 h-4 text-purple-400" />
                    Advanced Settings
                  </Label>
                  <Switch
                    checked={showAdvanced}
                    onCheckedChange={setShowAdvanced}
                    disabled={isCreating}
                  />
                </div>

                {/* Advanced Settings */}
                {showAdvanced && (
                  <div className="space-y-3 border-t border-gray-700 pt-3">
                    {/* Steps */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-200">
                        Inference Steps: {formData.steps}
                      </Label>
                      <Slider
                        value={[formData.steps]}
                        onValueChange={(value) =>
                          handleInputChange("steps", value[0])
                        }
                        min={10}
                        max={50}
                        step={1}
                        disabled={isCreating}
                        className="w-full"
                      />
                      <p className="text-xs text-gray-400">
                        More steps = higher quality but slower generation
                      </p>
                    </div>

                    {/* Guidance Scale */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-200">
                        Guidance Scale: {formData.guidanceScale}
                      </Label>
                      <Slider
                        value={[formData.guidanceScale]}
                        onValueChange={(value) =>
                          handleInputChange("guidanceScale", value[0])
                        }
                        min={1}
                        max={20}
                        step={0.5}
                        disabled={isCreating}
                        className="w-full"
                      />
                      <p className="text-xs text-gray-400">
                        Higher values follow the prompt more closely
                      </p>
                    </div>

                    {/* Seed */}
                    <div className="space-y-2">
                      <Label
                        htmlFor="seed"
                        className="text-sm font-medium text-gray-200"
                      >
                        Seed (-1 for random)
                      </Label>
                      <Input
                        id="seed"
                        type="number"
                        placeholder="-1"
                        value={formData.seed}
                        onChange={(e) =>
                          handleInputChange(
                            "seed",
                            parseInt(e.target.value) || -1
                          )
                        }
                        className="bg-gray-800 border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500"
                        disabled={isCreating}
                      />
                    </div>

                    {/* Safety Checker */}
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium text-gray-200">
                        Enable Safety Checker
                      </Label>
                      <Switch
                        checked={formData.enableSafetyChecker}
                        onCheckedChange={(checked) =>
                          handleInputChange("enableSafetyChecker", checked)
                        }
                        disabled={isCreating}
                      />
                    </div>

                    {/* Auto Upscale */}
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium text-gray-200">
                          Auto-Upscale to 4K
                        </Label>
                        <p className="text-xs text-gray-400 mt-1">
                          Automatically upscale generated photos to 4096×4096
                        </p>
                      </div>
                      <Switch
                        checked={formData.autoUpscale}
                        onCheckedChange={(checked) =>
                          handleInputChange("autoUpscale", checked)
                        }
                        disabled={isCreating}
                      />
                    </div>
                  </div>
                )}

                {/* Example Prompts */}
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-purple-400 mb-2 flex items-center gap-1">
                    <Wand2 className="w-4 h-4" />
                    Example Prompts
                  </h4>
                  <div className="space-y-2 text-xs text-gray-300">
                    <p>
                      • "Professional portrait photography with soft lighting
                      and elegant poses"
                    </p>
                    <p>
                      • "Casual lifestyle content with warm, natural lighting
                      and cozy settings"
                    </p>
                    <p>
                      • "Fashion photography with bold colors and dynamic poses"
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={isCreating}
                    className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white bg-transparent"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={!isFormValid || isCreating}
                    className="bg-gradient-to-r from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800 text-white font-semibold"
                  >
                    {isCreating ? (
                      <>
                        <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Creating Photo...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Create Photo
                      </>
                    )}
                  </Button>
                </div>
              </form>
            )}

          {/* Footer Info */}
          <div className="mt-4 p-4 bg-purple-900/20 border border-purple-800/30 rounded-lg">
            <div className="flex items-start gap-3">
              <ImageIcon className="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="text-purple-300 font-medium mb-1">
                  4K Real Photo Generation
                </p>
                <p className="text-gray-400 text-xs leading-relaxed">
                  Your photo generator will create ultra-high-resolution 4K images
                  (1024×1024 pixels) using advanced AI technology. This provides
                  exceptional detail and quality for professional use. Configure
                  the photo settings and advanced parameters to customize the
                  generation process. The photo generator will be ready for use immediately
                  after creation.
                </p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
