"use client";

import React, { useState, useEffect } from "react";
import { Download, Calendar, Settings, Image as ImageIcon, Zap } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { UpscaleImageModal } from "@/components/upscale-image-modal";

interface GenerationRecord {
  id: string;
  modelId: string;
  prompt: string;
  negativePrompt?: string;
  parameters: {
    width: number;
    height: number;
    steps: number;
    guidanceScale: number;
    seed: number;
    model: string;
  };
  images: Array<{
    url: string;
    filename: string;
  }>;
  generatedAt: string;
}

interface GenerationGalleryProps {
  modelId?: string;
  limit?: number;
}

export function GenerationGallery({ modelId, limit = 20 }: GenerationGalleryProps) {
  const [generations, setGenerations] = useState<GenerationRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [upscaleModal, setUpscaleModal] = useState<{
    isOpen: boolean;
    imageUrl: string;
    modelId: string;
    width?: number;
    height?: number;
  }>({
    isOpen: false,
    imageUrl: "",
    modelId: "",
  });

  useEffect(() => {
    fetchGenerations();
  }, [modelId, limit]);

  const fetchGenerations = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (modelId) params.append("modelId", modelId);
      if (limit) params.append("limit", limit.toString());

      const response = await fetch(`/api/generations?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch generations");
      }

      const data = await response.json();
      setGenerations(data.generations);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  const downloadImage = async (imageUrl: string, filename: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Error downloading image:", error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
        <span className="ml-2 text-gray-400">Loading generations...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-400">Error: {error}</p>
        <Button onClick={fetchGenerations} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  if (generations.length === 0) {
    return (
      <div className="text-center p-8">
        <ImageIcon className="w-12 h-12 text-gray-500 mx-auto mb-4" />
        <p className="text-gray-400">No generations found</p>
        {modelId && (
          <p className="text-sm text-gray-500 mt-2">
            Start generating images with this model to see them here
          </p>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">
          Generated Images {modelId && `for Model ${modelId}`}
        </h3>
        <Badge variant="secondary" className="bg-purple-900/50 text-purple-300">
          {generations.length} generation{generations.length !== 1 ? "s" : ""}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {generations.map((generation) => (
          <Card key={generation.id} className="bg-gray-800 border-gray-700">
            <CardContent className="p-4">
              {/* Images */}
              <div className="space-y-3 mb-4">
                {generation.images.map((image, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={image.url}
                      alt={`Generated image ${index + 1}`}
                      className="w-full h-48 object-cover rounded-lg bg-gray-700"
                      onError={(e) => {
                        // Fallback if image fails to load
                        e.currentTarget.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzc0MTUxIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlDQTNBRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=";
                      }}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center">
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => downloadImage(image.url, image.filename)}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          Download
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          className="opacity-0 group-hover:opacity-100 transition-opacity bg-blue-600 hover:bg-blue-700"
                          onClick={() => setUpscaleModal({
                            isOpen: true,
                            imageUrl: image.url,
                            modelId: generation.modelId,
                            width: generation.parameters.width,
                            height: generation.parameters.height,
                          })}
                        >
                          <Zap className="w-4 h-4 mr-1" />
                          Upscale 4K
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Prompt */}
              <div className="mb-3">
                <p className="text-sm text-gray-300 line-clamp-3">
                  {generation.prompt}
                </p>
                {generation.negativePrompt && (
                  <p className="text-xs text-gray-500 mt-1">
                    Negative: {generation.negativePrompt}
                  </p>
                )}
              </div>

              {/* Parameters */}
              <div className="flex flex-wrap gap-2 mb-3">
                <Badge variant="outline" className="text-xs">
                  {generation.parameters.width}×{generation.parameters.height}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {generation.parameters.steps} steps
                </Badge>
                <Badge variant="outline" className="text-xs">
                  CFG {generation.parameters.guidanceScale}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Seed {generation.parameters.seed}
                </Badge>
              </div>

              {/* Metadata */}
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {formatDate(generation.generatedAt)}
                </div>
                <div className="flex items-center gap-1">
                  <Settings className="w-3 h-3" />
                  {generation.parameters.model.replace("stable-diffusion-", "SD ")}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Upscale Modal */}
      <UpscaleImageModal
        isOpen={upscaleModal.isOpen}
        onClose={() => setUpscaleModal({ ...upscaleModal, isOpen: false })}
        imageUrl={upscaleModal.imageUrl}
        modelId={upscaleModal.modelId}
        originalWidth={upscaleModal.width}
        originalHeight={upscaleModal.height}
      />
    </div>
  );
}
