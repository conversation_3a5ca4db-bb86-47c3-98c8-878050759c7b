"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Loader2, Download, Zap, Image as ImageIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface UpscaleImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  modelId: string;
  originalWidth?: number;
  originalHeight?: number;
}

interface UpscaleResult {
  success: boolean;
  upscaledImage: {
    url: string | null;
    base64: string;
  };
  parameters: {
    originalWidth: number;
    originalHeight: number;
    upscaledWidth: number;
    upscaledHeight: number;
  };
  originalImage: {
    url: string;
    width: number;
    height: number;
  };
  message?: string;
  error?: string;
}

export function UpscaleImageModal({
  isOpen,
  onClose,
  imageUrl,
  modelId,
  originalWidth = 1024,
  originalHeight = 1024,
}: UpscaleImageModalProps) {
  const [isUpscaling, setIsUpscaling] = useState(false);
  const [upscaleResult, setUpscaleResult] = useState<UpscaleResult | null>(null);
  const { toast } = useToast();

  const handleUpscale = async () => {
    setIsUpscaling(true);
    setUpscaleResult(null);

    try {
      const response = await fetch("/api/images/upscale", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          imageUrl,
          modelId,
        }),
      });

      const result: UpscaleResult = await response.json();

      if (result.success) {
        setUpscaleResult(result);
        toast({
          title: "✅ Upscaling Complete!",
          description: result.message || "Image successfully upscaled to 4096x4096",
        });
      } else {
        throw new Error(result.error || "Upscaling failed");
      }
    } catch (error) {
      console.error("Upscaling error:", error);
      toast({
        title: "❌ Upscaling Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsUpscaling(false);
    }
  };

  const handleDownload = (imageData: string, filename: string) => {
    try {
      // Create download link
      const link = document.createElement("a");
      link.href = imageData;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "📥 Download Started",
        description: `Downloading ${filename}`,
      });
    } catch (error) {
      toast({
        title: "❌ Download Failed",
        description: "Could not download the image",
        variant: "destructive",
      });
    }
  };

  const handleClose = () => {
    setUpscaleResult(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-500" />
            Upscale Image to 4K Resolution
          </DialogTitle>
          <DialogDescription>
            Enhance your image from {originalWidth}×{originalHeight} to 4096×4096 using AI upscaling
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Original Image Preview */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-4 w-4" />
              <span className="font-medium">Original Image</span>
              <Badge variant="secondary">
                {originalWidth}×{originalHeight}
              </Badge>
            </div>
            <div className="border rounded-lg overflow-hidden bg-gray-50">
              <img
                src={imageUrl}
                alt="Original image"
                className="w-full h-auto max-h-64 object-contain"
              />
            </div>
          </div>

          {/* Upscaling Controls */}
          {!upscaleResult && (
            <div className="flex flex-col items-center gap-4 py-6">
              <Button
                onClick={handleUpscale}
                disabled={isUpscaling}
                size="lg"
                className="min-w-[200px]"
              >
                {isUpscaling ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Upscaling...
                  </>
                ) : (
                  <>
                    <Zap className="mr-2 h-4 w-4" />
                    Upscale to 4K
                  </>
                )}
              </Button>
              <p className="text-sm text-gray-600 text-center">
                This will enhance your image to 4096×4096 resolution using Stability AI's upscaling technology.
                The process may take 30-60 seconds.
              </p>
            </div>
          )}

          {/* Upscaled Result */}
          {upscaleResult && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-green-500" />
                <span className="font-medium">Upscaled Image</span>
                <Badge variant="default" className="bg-green-500">
                  {upscaleResult.parameters.upscaledWidth}×{upscaleResult.parameters.upscaledHeight}
                </Badge>
              </div>
              
              <div className="border rounded-lg overflow-hidden bg-gray-50">
                <img
                  src={upscaleResult.upscaledImage.base64}
                  alt="Upscaled image"
                  className="w-full h-auto max-h-96 object-contain"
                />
              </div>

              <div className="flex gap-2 justify-center">
                <Button
                  onClick={() =>
                    handleDownload(
                      upscaleResult.upscaledImage.base64,
                      `upscaled_4096x4096_${Date.now()}.png`
                    )
                  }
                  variant="outline"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download 4K Image
                </Button>
                
                {upscaleResult.upscaledImage.url && (
                  <Button
                    onClick={() => window.open(upscaleResult.upscaledImage.url!, '_blank')}
                    variant="outline"
                  >
                    <ImageIcon className="mr-2 h-4 w-4" />
                    View Full Size
                  </Button>
                )}
              </div>

              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>Original:</strong> {upscaleResult.parameters.originalWidth}×{upscaleResult.parameters.originalHeight}</p>
                <p><strong>Upscaled:</strong> {upscaleResult.parameters.upscaledWidth}×{upscaleResult.parameters.upscaledHeight}</p>
                <p><strong>Enhancement:</strong> 4× resolution increase</p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            {upscaleResult ? "Close" : "Cancel"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
