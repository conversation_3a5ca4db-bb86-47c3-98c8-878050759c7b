"use client";

import type React from "react";

import { useState, useRef, useEffect } from "react";
import { Send, <PERSON>rk<PERSON>, User, Loader2, CheckCircle } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";

interface UseModelModalProps {
  isOpen: boolean;
  onClose: () => void;
  modelName: string;
  modelPrompt: string;
  modelId?: string;
}

interface ChatMessage {
  id: number;
  sender: "user" | "ai";
  text: string;
  images?: Array<{
    url: string;
    base64: string;
  }>;
  isGenerating?: boolean;
  generationId?: string;
  imagePaths?: string[];
  isSuccess?: boolean;
  isTyping?: boolean;
}

export function UseModelModal({
  isOpen,
  onClose,
  modelName,
  modelPrompt,
  modelId = "default_model",
}: UseModelModalProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [userInput, setUserInput] = useState("");
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Add initial AI message when modal opens
    if (isOpen && messages.length === 0) {
      setMessages([
        {
          id: 1,
          sender: "ai",
          text: `Hello! I'm Steve Jobs. I'm here to share my vision and insights with you. Whether you want to discuss innovation, design, technology, or building something truly revolutionary - I'm ready to help you think different. What's on your mind?`,
        },
      ]);
    }
  }, [isOpen, messages.length, modelName, modelPrompt]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (userInput.trim() === "") return;

    const newUserMessage: ChatMessage = {
      id: messages.length + 1,
      sender: "user",
      text: userInput.trim(),
    };

    // Add user message
    setMessages((prev) => [...prev, newUserMessage]);
    setUserInput("");
    setIsSending(true);

    // Add typing message
    const typingMessage: ChatMessage = {
      id: messages.length + 2,
      sender: "ai",
      text: "Steve is thinking...",
      isTyping: true,
    };
    setMessages((prev) => [...prev, typingMessage]);

    try {
      // Get conversation history for context (excluding the typing message)
      const conversationHistory = messages
        .filter((msg) => !msg.isGenerating && !msg.isTyping)
        .map((msg) => ({
          role:
            msg.sender === "user" ? ("user" as const) : ("assistant" as const),
          content: msg.text,
        }));

      // Call the Steve Jobs chat API
      console.log("Calling Steve Jobs API...");
      console.log("Request URL:", "/api/chat/steve-jobs");
      console.log("Request payload:", {
        message: newUserMessage.text.substring(0, 100) + "...",
        conversationLength: conversationHistory.length,
      });

      let response;
      try {
        // Temporarily use test endpoint to debug
        console.log("🧪 Using test endpoint for debugging...");
        response = await fetch("/api/chat/steve-jobs", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            message: newUserMessage.text,
            conversation: conversationHistory,
          }),
        });

        console.log("Response received:", {
          ok: response.ok,
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
        });
      } catch (fetchError) {
        console.error("Fetch error:", fetchError);
        throw new Error(
          `Network error: ${
            fetchError instanceof Error
              ? fetchError.message
              : String(fetchError)
          }`
        );
      }

      if (!response.ok) {
        let errorText = "";
        try {
          errorText = await response.text();
        } catch (textError) {
          console.error("Error reading response text:", textError);
          errorText = "Could not read error response";
        }

        console.error("Steve Jobs API Error Details:", {
          status: response.status,
          statusText: response.statusText,
          errorText: errorText,
          url: response.url,
          headers: Object.fromEntries(response.headers.entries()),
        });

        throw new Error(
          `API Error ${response.status}: ${response.statusText}${
            errorText ? ` - ${errorText}` : ""
          }`
        );
      }

      const result = await response.json();
      console.log("✅ Test response received successfully:", result);

      // Replace typing message with test response
      const aiResponse: ChatMessage = {
        id: messages.length + 2,
        sender: "ai",
        text: result.message || "Test response received",
        isSuccess: true,
      };

      setMessages((prev) => prev.slice(0, -1).concat(aiResponse));
    } catch (error) {
      console.error("Error chatting with Steve Jobs:", error);

      // Replace typing message with error
      const errorResponse: ChatMessage = {
        id: messages.length + 2,
        sender: "ai",
        text: "I apologize, but I'm having trouble connecting right now. Even the best technology sometimes needs a moment to reboot. Please try again.",
      };

      setMessages((prev) => prev.slice(0, -1).concat(errorResponse));
    }

    setIsSending(false);
  };

  const handleClose = () => {
    setMessages([]); // Clear messages on close
    setUserInput("");
    setIsSending(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl h-[80vh] flex flex-col bg-gray-900 border-gray-700 text-white dark">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-bold bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-400" />
            Amazing chat
          </DialogTitle>
          <DialogDescription className="text-gray-400 line-clamp-1">
            Powered by GPT-4o
          </DialogDescription>
        </DialogHeader>

        <Card className="flex-1 flex flex-col border-gray-700 bg-gray-800 overflow-hidden">
          <CardContent className="flex-1 p-4 overflow-hidden">
            <ScrollArea className="h-full pr-4">
              <div className="space-y-4">
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex items-start gap-3 ${
                      msg.sender === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    {msg.sender === "ai" && (
                      <div className="w-8 h-8 flex-shrink-0 rounded-full bg-purple-600 flex items-center justify-center text-white">
                        <Sparkles className="w-4 h-4" />
                      </div>
                    )}
                    <div
                      className={`max-w-[70%] p-3 rounded-lg ${
                        msg.sender === "user"
                          ? "bg-yellow-600 text-black rounded-br-none"
                          : msg.isSuccess
                          ? "bg-green-800 text-white rounded-bl-none border border-green-600"
                          : "bg-gray-700 text-white rounded-bl-none"
                      }`}
                    >
                      <div className="flex items-start gap-2 mb-2">
                        {msg.isSuccess && (
                          <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                        )}
                        <p className="text-sm whitespace-pre-line">
                          {msg.text}
                        </p>
                        {(msg.isGenerating || msg.isTyping) && (
                          <Loader2 className="w-4 h-4 animate-spin flex-shrink-0" />
                        )}
                      </div>
                    </div>
                    {msg.sender === "user" && (
                      <div className="w-8 h-8 flex-shrink-0 rounded-full bg-gray-600 flex items-center justify-center text-white">
                        <User className="w-4 h-4" />
                      </div>
                    )}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
          </CardContent>
          <CardFooter className="p-4 border-t border-gray-700">
            <form onSubmit={handleSendMessage} className="flex w-full gap-2">
              <Input
                placeholder="Ask Steve Jobs anything about innovation, design, technology..."
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                className="flex-1 bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500"
                disabled={isSending}
              />
              <Button
                type="submit"
                disabled={isSending || userInput.trim() === ""}
                className="bg-gradient-to-r from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800 text-white font-semibold"
              >
                {isSending ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </Button>
            </form>
          </CardFooter>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
