# Image Upscaling Guide

This guide explains how to use the new 4K image upscaling functionality in SpireClub.

## Overview

The SpireClub application now includes AI-powered image upscaling that enhances your generated images from 1024×1024 to 4096×4096 resolution using Stability AI's upscaling technology.

## Features

### 🚀 Manual Upscaling
- **Upscale Button**: Click the "Upscale 4K" button on any generated image in the gallery
- **Interactive Modal**: Preview both original and upscaled versions side-by-side
- **Download Options**: Download the 4K upscaled image directly
- **File Management**: Upscaled images are automatically saved in organized folders

### ⚡ Auto-Upscaling
- **Model Creation**: Enable auto-upscaling when creating new AI models
- **Generation Interface**: Toggle auto-upscaling on/off in the generation interface
- **Automatic Processing**: Images are automatically upscaled after generation
- **Dual Output**: Get both original and 4K versions of your images

## How to Use

### Manual Upscaling

1. **Generate an Image**: Create images using any of your AI models
2. **Open Gallery**: View your generated images in the gallery
3. **Hover Over Image**: Hover over any image to reveal action buttons
4. **Click Upscale**: Click the blue "Upscale 4K" button
5. **Wait for Processing**: The upscaling process takes 30-60 seconds
6. **Download Result**: Download your 4096×4096 enhanced image

### Auto-Upscaling

#### During Model Creation:
1. **Create New Model**: Click "Create New Model"
2. **Advanced Settings**: Enable "Advanced Settings" toggle
3. **Auto-Upscale Option**: Turn on "Auto-Upscale to 4K"
4. **Create Model**: Complete the model creation process

#### During Image Generation:
1. **Use Model**: Click "Use Model" on any existing model
2. **Auto-Upscale Toggle**: Enable the "Auto-Upscale to 4K" switch at the bottom
3. **Generate Images**: Enter your prompt and generate
4. **Automatic Processing**: Images will be automatically upscaled after generation

## File Organization

### Directory Structure
```
public/generated-images/
├── model_123456789/
│   ├── original_image.png          # Original 1024×1024 image
│   └── upscaled/
│       └── original_image_upscaled_4096x4096.png  # 4K upscaled version
```

### File Naming Convention
- **Original Images**: `{generationId}_{index}_{timestamp}.png`
- **Upscaled Images**: `{generationId}_{index}_upscaled_4096x4096.png`

## API Endpoints

### POST /api/images/upscale
Upscale an existing image to 4096×4096 resolution.

**Request Body:**
```json
{
  "imageUrl": "/generated-images/model_123/image.png",
  "modelId": "model_123"
}
```

**Response:**
```json
{
  "success": true,
  "upscaledImage": {
    "url": "/generated-images/model_123/upscaled/image_upscaled_4096x4096.png",
    "base64": "data:image/png;base64,..."
  },
  "parameters": {
    "originalWidth": 1024,
    "originalHeight": 1024,
    "upscaledWidth": 4096,
    "upscaledHeight": 4096
  },
  "message": "Image successfully upscaled to 4096x4096"
}
```

### POST /api/models/generate (Enhanced)
Generate images with optional auto-upscaling.

**New Parameter:**
```json
{
  "modelId": "model_123",
  "prompt": "A beautiful landscape",
  "autoUpscale": true  // New: Enable auto-upscaling
}
```

## Technical Details

### Upscaling Technology
- **Provider**: Stability AI Conservative Upscaler
- **API Endpoint**: `https://api.stability.ai/v2beta/stable-image/upscale/conservative`
- **Enhancement Factor**: 4× resolution increase
- **Quality**: High-quality AI enhancement preserving details

### Performance
- **Processing Time**: 30-60 seconds per image
- **File Size**: 4K images are significantly larger (typically 5-20MB)
- **Quality**: Professional-grade upscaling suitable for printing and high-resolution displays

### Requirements
- **API Key**: Valid Stability AI API key required
- **Provider**: Must use "stability" provider (not Replicate)
- **Credits**: Upscaling consumes Stability AI API credits

## Setup Instructions

### Environment Variables
Ensure you have the following environment variables set:

```bash
STABLE_DIFFUSION_PROVIDER=stability
STABILITY_AI_API_KEY=your_stability_ai_api_key_here
```

### Testing the API
Use the provided test script to verify upscaling functionality:

```bash
# Test with minimal image
node test-upscale-api.js

# Test with real image file
node test-upscale-api.js path/to/your/image.png
```

## Troubleshooting

### Common Issues

1. **"Upscaling is only supported with Stability AI provider"**
   - Ensure `STABLE_DIFFUSION_PROVIDER=stability` in your environment
   - Verify you're not using Replicate provider

2. **"Stability AI API not configured"**
   - Check that `STABILITY_AI_API_KEY` is set correctly
   - Verify your API key is valid and has sufficient credits

3. **"Failed to read original image file"**
   - Ensure the original image exists in the file system
   - Check file permissions and paths

4. **Upscaling takes too long**
   - Normal processing time is 30-60 seconds
   - Check your internet connection
   - Verify Stability AI service status

### Error Handling
- **Graceful Degradation**: If upscaling fails, original images are still available
- **User Feedback**: Clear error messages guide users to solutions
- **Retry Capability**: Users can retry failed upscaling operations

## Best Practices

### When to Use Upscaling
- **High-Resolution Prints**: For physical printing and large displays
- **Professional Work**: When maximum quality is required
- **Detail Enhancement**: To reveal fine details in generated images
- **Archival Purposes**: For long-term storage of high-quality versions

### Performance Optimization
- **Selective Upscaling**: Only upscale images you actually need in 4K
- **Batch Processing**: Consider upscaling multiple images during off-peak hours
- **Storage Management**: Monitor disk space as 4K images are much larger

### Cost Management
- **Credit Monitoring**: Keep track of Stability AI API usage
- **Selective Auto-Upscaling**: Only enable auto-upscaling for important models
- **Manual Control**: Use manual upscaling for better cost control

## Future Enhancements

### Planned Features
- **Multiple Upscaling Factors**: 2×, 4×, 8× options
- **Batch Upscaling**: Process multiple images simultaneously
- **Quality Presets**: Different quality/speed trade-offs
- **Progress Tracking**: Real-time upscaling progress indicators
- **Cloud Storage Integration**: Direct upload to cloud storage services

### Integration Possibilities
- **Print Services**: Direct integration with printing services
- **Social Media**: Optimized exports for different platforms
- **NFT Platforms**: High-resolution versions for NFT marketplaces
- **Portfolio Tools**: Integration with portfolio and gallery systems

## Support

For issues or questions about the upscaling functionality:

1. **Check Environment**: Verify API keys and provider settings
2. **Test API**: Run the test script to diagnose connection issues
3. **Review Logs**: Check browser console and server logs for errors
4. **Documentation**: Refer to Stability AI's official documentation
5. **Community**: Join discussions about image generation and upscaling

---

**Note**: This feature requires a valid Stability AI API key and consumes API credits. Monitor your usage to avoid unexpected charges.
