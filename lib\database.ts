// Database utilities for storing Stable Diffusion generations
// This is a simple file-based storage system. In production, use a proper database.

import { writeFile, readFile, mkdir } from "fs/promises";
import { join } from "path";
import { existsSync } from "fs";

export interface GenerationRecord {
  id: string;
  modelId: string;
  prompt: string;
  negativePrompt?: string;
  parameters: {
    width: number;
    height: number;
    steps: number;
    guidanceScale: number;
    seed: number;
    model: string;
  };
  images: Array<{
    url: string;
    filename: string;
  }>;
  generatedAt: string;
  userId?: string; // For future user authentication
}

const DB_DIR = join(process.cwd(), "data");
const GENERATIONS_FILE = join(DB_DIR, "generations.json");

// Ensure database directory exists
async function ensureDbDir() {
  if (!existsSync(DB_DIR)) {
    await mkdir(DB_DIR, { recursive: true });
  }
}

// Load all generations from file
async function loadGenerations(): Promise<GenerationRecord[]> {
  try {
    await ensureDbDir();
    if (!existsSync(GENERATIONS_FILE)) {
      return [];
    }
    const data = await readFile(GENERATIONS_FILE, "utf-8");
    return JSON.parse(data);
  } catch (error) {
    console.error("Error loading generations:", error);
    return [];
  }
}

// Save generations to file
async function saveGenerations(generations: GenerationRecord[]): Promise<void> {
  try {
    await ensureDbDir();
    await writeFile(GENERATIONS_FILE, JSON.stringify(generations, null, 2));
  } catch (error) {
    console.error("Error saving generations:", error);
    throw error;
  }
}

// Add a new generation record
export async function saveGenerationRecord(record: GenerationRecord): Promise<void> {
  const generations = await loadGenerations();
  generations.push(record);
  await saveGenerations(generations);
}

// Get generations by model ID
export async function getGenerationsByModel(modelId: string): Promise<GenerationRecord[]> {
  const generations = await loadGenerations();
  return generations.filter(gen => gen.modelId === modelId);
}

// Get all generations (with optional limit)
export async function getAllGenerations(limit?: number): Promise<GenerationRecord[]> {
  const generations = await loadGenerations();
  const sorted = generations.sort((a, b) => 
    new Date(b.generatedAt).getTime() - new Date(a.generatedAt).getTime()
  );
  return limit ? sorted.slice(0, limit) : sorted;
}

// Get generation by ID
export async function getGenerationById(id: string): Promise<GenerationRecord | null> {
  const generations = await loadGenerations();
  return generations.find(gen => gen.id === id) || null;
}
