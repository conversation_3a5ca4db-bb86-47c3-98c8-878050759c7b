// OpenAI GPT-4o Integration
// This file contains utilities for integrating with OpenAI's GPT-4o model

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatResponse {
  message: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class OpenAIClient {
  private apiKey: string;
  private baseUrl = "https://api.openai.com/v1";

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async chat(messages: ChatMessage[], model: string = "gpt-4o"): Promise<ChatResponse> {
    console.log("Calling OpenAI API with model:", model, "- Messages:", messages.length);

    const requestBody = {
      model: model,
      messages: messages,
      max_tokens: 1000,
      temperature: 0.7,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
    };

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("OpenAI API error:", {
        status: response.status,
        statusText: response.statusText,
        errorText: errorText
      });
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log("OpenAI response received successfully");

    return {
      message: result.choices[0]?.message?.content || "",
      usage: result.usage,
    };
  }
}

// Environment configuration
export const getOpenAIConfig = () => {
  const apiKey = process.env.OPENAI_API_KEY;
  const systemPrompt = process.env.STEVE_JOBS_SYSTEM_PROMPT;

  if (!apiKey) {
    throw new Error(
      "OpenAI API key not configured. Please set OPENAI_API_KEY in your environment variables."
    );
  }

  if (!systemPrompt) {
    throw new Error(
      "Steve Jobs system prompt not configured. Please set STEVE_JOBS_SYSTEM_PROMPT in your environment variables."
    );
  }

  return { apiKey, systemPrompt };
};

// Factory function to create OpenAI client
export function createOpenAIClient(): OpenAIClient {
  const { apiKey } = getOpenAIConfig();
  return new OpenAIClient(apiKey);
}

// Helper function to create a chat with Steve Jobs system prompt
export async function chatWithSteveJobs(userMessage: string): Promise<ChatResponse> {
  const { systemPrompt } = getOpenAIConfig();
  const client = createOpenAIClient();

  const messages: ChatMessage[] = [
    {
      role: 'system',
      content: systemPrompt,
    },
    {
      role: 'user',
      content: userMessage,
    },
  ];

  return await client.chat(messages);
}
