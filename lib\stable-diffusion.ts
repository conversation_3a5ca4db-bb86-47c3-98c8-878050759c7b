// Stable Diffusion Integration Utilities
// This file contains utilities for integrating with various Stable Diffusion APIs

import { Form } from "react-hook-form";

export interface StableDiffusionConfig {
  model: string;
  prompt: string;
  negative_prompt?: string;
  width: number;
  height: number;
  num_inference_steps: number;
  guidance_scale: number;
  seed?: number;
  safety_checker: boolean;
}

export interface GenerationResult {
  images: string[];
  parameters: {
    prompt: string;
    negative_prompt?: string;
    width: number;
    height: number;
    num_inference_steps: number;
    guidance_scale: number;
    seed: number;
  };
}

export interface UpscaleResult {
  image: string;
  parameters: {
    originalWidth: number;
    originalHeight: number;
    upscaledWidth: number;
    upscaledHeight: number;
  };
}

// Stability AI API Integration
export class StabilityAIClient {
  private apiKey: string;
  private baseUrl = "https://api.stability.ai";
  private upscaleUrl =
    "https://api.stability.ai/v2beta/stable-image/upscale/fast";

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateImage(
    config: StableDiffusionConfig
  ): Promise<GenerationResult> {
    console.log("Calling Stability AI API with config:", {
      model: config.model,
      prompt: config.prompt.substring(0, 50) + "...",
      width: config.width,
      height: config.height,
    });

    const response = await fetch(
      `${this.baseUrl}/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.apiKey}`,
          Accept: "application/json",
        },
        body: JSON.stringify({
          text_prompts: [
            {
              text: config.prompt,
              weight: 1,
            },
            ...(config.negative_prompt
              ? [
                  {
                    text: config.negative_prompt,
                    weight: -1,
                  },
                ]
              : []),
          ],
          cfg_scale: config.guidance_scale,
          height: config.height,
          width: config.width,
          steps: config.num_inference_steps,
          samples: 1,
          seed: config.seed,
        }),
      }
    );

    console.log("Stability AI API response status:", response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Stability AI API error:", response.status, errorText);
      throw new Error(
        `Stability AI API error: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const result = await response.json();
    console.log(
      "Stability AI API success, received",
      result.artifacts?.length || 0,
      "images"
    );

    const base64Data = result.artifacts[0].base64;
    const imageBuffer = Buffer.from(base64Data, "base64");

    const payload = {
      image: imageBuffer,
      output_format: "webp"
    };

    // Send request to upscale API
    const upscale_response = await fetch("https://api.stability.ai/v2beta/stable-image/upscale/fast", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        Accept: "image/*",
      },
      body: new FormData(),
    });

    // Check response
    if (!upscale_response.ok) {
      const errorText = await upscale_response.text();
      throw new Error(`Upscale failed: ${upscale_response.status} - ${errorText}`);
    }

    // Save result
    const upscaleImageBuffer = await upscale_response.arrayBuffer();
    fs.writeFileSync("./upscaled-image.png", Buffer.from(upscaleImageBuffer));


    return {
      images: result.artifacts.map(
        (artifact: any) => `data:image/png;base64,${artifact.base64}`
      ),
      parameters: {
        prompt: config.prompt,
        negative_prompt: config.negative_prompt,
        width: config.width,
        height: config.height,
        num_inference_steps: config.num_inference_steps,
        guidance_scale: config.guidance_scale,
        seed: config.seed || Math.floor(Math.random() * 1000000),
      },
    };
  }

  async upscaleImage(
    imageData: string,
    originalWidth: number,
    originalHeight: number
  ): Promise<UpscaleResult> {
    console.log("Calling Stability AI Upscale API with image:", {
      originalWidth,
      originalHeight,
      targetSize: "4096x4096",
    });

    // Remove data URL prefix if present
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, "");

    // Create form data for the upscale API
    const formData = new FormData();

    // Convert base64 to blob using browser-compatible method
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    const imageBlob = new Blob([bytes], { type: "image/png" });

    formData.append("image", imageBlob, "image.png");
    formData.append("output_format", "png");

    const response = await fetch(
      `${this.baseUrl}/v2beta/stable-image/upscale/conservative`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          Accept: "image/*",
        },
        body: formData,
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Stability AI Upscale API error:", errorText);
      throw new Error(
        `Stability AI Upscale API error: ${response.status} ${response.statusText}`
      );
    }

    // Get the upscaled image as array buffer
    const imageBuffer = await response.arrayBuffer();
    const bytes2 = new Uint8Array(imageBuffer);
    let binaryString2 = "";
    for (let i = 0; i < bytes2.length; i++) {
      binaryString2 += String.fromCharCode(bytes2[i]);
    }
    const base64Image = btoa(binaryString2);
    const dataUrl = `data:image/png;base64,${base64Image}`;

    return {
      image: dataUrl,
      parameters: {
        originalWidth,
        originalHeight,
        upscaledWidth: 4096,
        upscaledHeight: 4096,
      },
    };
  }
}

// Replicate API Integration
export class ReplicateClient {
  private apiKey: string;
  private baseUrl = 'https://api.replicate.com/v1';
  
  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateImage(
    config: StableDiffusionConfig
  ): Promise<GenerationResult> {
    // Create prediction
    const response = await fetch(`${this.baseUrl}/predictions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Token ${this.apiKey}`,
      },
      body: JSON.stringify({
        version: this.getModelVersion(config.model),
        input: {
          prompt: config.prompt,
          negative_prompt: config.negative_prompt || "",
          width: config.width,
          height: config.height,
          num_inference_steps: config.num_inference_steps,
          guidance_scale: config.guidance_scale,
          seed: config.seed,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Replicate API error: ${response.statusText}`);
    }

    const prediction = await response.json();

    // Poll for completion
    const result = await this.waitForCompletion(prediction.id);

    return {
      images: Array.isArray(result.output) ? result.output : [result.output],
      parameters: {
        prompt: config.prompt,
        negative_prompt: config.negative_prompt,
        width: config.width,
        height: config.height,
        num_inference_steps: config.num_inference_steps,
        guidance_scale: config.guidance_scale,
        seed: config.seed || Math.floor(Math.random() * 1000000),
      },
    };
  }

  private getModelVersion(model: string): string {
    const modelVersions: Record<string, string> = {
      "stable-diffusion-xl-base-1.0":
        "stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
      "stable-diffusion-v1-5":
        "stability-ai/stable-diffusion:db21e45d3f7023abc2a46ee38a23973f6dce16bb082a930b0c49861f96d1e5bf",
      "stable-diffusion-2-1":
        "stability-ai/stable-diffusion:f178fa7a1ae43a9a9af01b833b9363a0a11ba824103b4f9b4c0a4b8b8b8b8b8b",
    };

    return (
      modelVersions[model] || modelVersions["stable-diffusion-xl-base-1.0"]
    );
  }

  private async waitForCompletion(predictionId: string): Promise<any> {
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes with 5-second intervals

    while (attempts < maxAttempts) {
      const response = await fetch(
        `${this.baseUrl}/predictions/${predictionId}`,
        {
          headers: {
            Authorization: `Token ${this.apiKey}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to check prediction status: ${response.statusText}`
        );
      }

      const prediction = await response.json();

      if (prediction.status === "succeeded") {
        return prediction;
      } else if (prediction.status === "failed") {
        throw new Error(`Prediction failed: ${prediction.error}`);
      }

      // Wait 5 seconds before next attempt
      await new Promise((resolve) => setTimeout(resolve, 5000));
      attempts++;
    }

    throw new Error("Prediction timed out");
  }
}

// Factory function to create the appropriate client based on configuration
export function createStableDiffusionClient(
  provider: "stability" | "replicate",
  apiKey: string
) {
  switch (provider) {
    case "stability":
      return new StabilityAIClient(apiKey);
    case "replicate":
      return new ReplicateClient(apiKey);
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}

// Environment configuration
export const getStableDiffusionConfig = () => {
  const provider =
    (process.env.STABLE_DIFFUSION_PROVIDER as "stability" | "replicate") ||
    "stability";
  const apiKey =
    provider === "stability"
      ? process.env.STABILITY_AI_API_KEY
      : process.env.REPLICATE_API_TOKEN;

  if (!apiKey) {
    throw new Error(
      `Missing API key for ${provider}. Please set ${
        provider === "stability"
          ? "STABILITY_AI_API_KEY"
          : "REPLICATE_API_TOKEN"
      } environment variable.`
    );
  }

  return { provider, apiKey };
};
