// Test script to verify Stability AI API connection
require('dotenv').config({ path: '.env.local' });

async function testStabilityAPI() {
  const apiKey = process.env.STABILITY_AI_API_KEY;
  
  if (!apiKey) {
    console.error("❌ No API key found in environment variables");
    return;
  }
  
  console.log("🔑 API Key found:", apiKey.substring(0, 10) + "...");
  
  try {
    console.log("🚀 Testing Stability AI API connection...");
    
    const response = await fetch(
      "https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
          "Accept": "application/json",
        },
        body: JSON.stringify({
          text_prompts: [
            {
              text: "A beautiful sunset over mountains",
              weight: 1,
            }
          ],
          cfg_scale: 7,
          height: 1024,
          width: 1024,
          steps: 20,
          samples: 1,
        }),
      }
    );
    
    console.log("📡 Response status:", response.status);
    console.log("📡 Response headers:", Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ API Error:", errorText);
      return;
    }
    
    const result = await response.json();
    console.log("✅ API Success!");
    console.log("📊 Result:", {
      artifacts: result.artifacts?.length || 0,
      firstImageSize: result.artifacts?.[0]?.base64?.length || 0
    });
    
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

testStabilityAPI();
